from decimal import Decimal

from exchange import BinanceFuturesAPI





if __name__ == "__main__":
    api_key = 'Dj6TX1A9k3jylcNYzIlkUnfb1xKtCIkgZrOp6HeaxaxXTc7EFz5FpO4xcyMtBHiW'
    secret_key = 'eZ4RfhVAGj4606CPGEYcO06knDfFynFKsPXkpevALX2jdfpl0gXtyqYiHyHQ0iwh'

    # 初始化API客户端
    print("\n🚀 自动交易清理程序启动")
    print("=" * 50)
    print("\n1. 初始化API客户端...")
    futures_api = BinanceFuturesAPI(api_key, secret_key)
    print("✅ API客户端初始化成功")

    try:
        # 第一步：处理已持仓订单，进行挂单平仓
        futures_api.process_positions()

        # 第二步：处理未成交订单，进行取消
        # 注意：这一步会处理部分成交的订单，取消未成交部分
        # 部分成交产生的持仓在第一步中已经处理了
        futures_api.cancel_not_transaction_orders()

        print("\n" + "=" * 50)
        print("🎉 自动交易清理程序执行完成")
        print("\n📋 执行总结:")
        print("   1. ✅ 已处理所有持仓，创建了相应的平仓订单")
        print("   2. ✅ 已取消所有未成交订单")
        print("   3. ✅ 部分成交订单的已成交部分已通过平仓订单处理")


        # 开始进行开清测试
        




    except Exception as e:
        print(f"\n❌ 程序执行过程中发生错误: {e}")
        print("请检查网络连接和API权限设置")
